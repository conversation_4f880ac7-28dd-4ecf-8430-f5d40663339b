<?php
const iPROID = 0;
const iPROCODE = 1;
const iPROCODE2 = 2;
const iPROMANNAME = 3;
const iPROTYPNAME = 4;
const iPRONAME = 5;
const iPRODESCS = 6;
const iPRODESC = 7;
const iPROTITLE = 8;
const iPROKEY = 9;
const iPROKEYWORDS = 10;
const iPROACCESS = 11;
const iPROWARRANTY = 12;
const iPROPRICECOM = 13;
const iPROPRICEA = 14;
const iPROPRICEB = 15;
const iPROPRICEC = 16;
const iPRONOTEINT = 17;
const iPROVATID = 18;
const iPROWEIGHT = 19;
const iPROPICNAME = 20;
const iPROCATIDS = 21;
const iPRONAMES = 22;
const iPROGOOGLEOFF = 23;
const iPROBIGSIZE = 24;
const iPROOFFER = 25;
const iPROSTATUS = 26;
const iPROORDER = 27;
const iPROURL = 28;
const iPARAMS_START = 29;

final class Admin_ImportPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function importSubmitted (NAppForm $form) {
    $fileName = "";
    $proparams = new ProParamsModel();
    $log = array();
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      //podle typu importu se rozhodnu
      if (!$vals["imp_file"]->isOk()) {
        $form->addError("Neplatný importní soubor.");
        return;
      } else {
        $fileName = $vals["imp_file"]->getTemporaryFile();
      }
      /** PHPExcel_IOFactory */
      include LIBS_DIR.'/PHPExcel/PHPExcel/IOFactory.php';
      $objPHPExcel = PHPExcel_IOFactory::load($fileName);
      $sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,False);

      $cnt = 0;
      $cntinsOK = 0;
      $cntinsER = 0;
      $cntupdOK = 0;
      $cntupdER = 0;
      $manArr = array();    
      $typArr = array();    
      $products = new ProductsModel();
      $proParams = new ProParamsModel();
      $catalogs = new CatalogsModel();    
  
      foreach ($sheetData AS $row) {
        $cnt++;
        if ($cnt <= 1) continue;      
        if (empty($row[iPROCODE])) {
          $log[] = "Není vyplněn kód zboží";
          continue;
        }  
        if (empty($row[iPRONAME])) {
          $log[] = $row[iPROCODE]."|Není vyplněn název zboží";
          continue;
        }  
        $arr = array();
        //naplnim pole pro ulozeni dat
        $arr["procode"] = trim($row[iPROCODE]);
        $arr["procode2"] = trim($row[iPROCODE2]);
        
        
        //zjistim ID vyrobce
        if (!array_key_exists($row[iPROMANNAME], $manArr)) {
          $manArr[$row[iPROMANNAME]] = $this->getManId($row[iPROMANNAME]);
        }
        $arr["promanid"] = $manArr[$row[iPROMANNAME]];
        //zjistim ID typu zbozi
        if (!array_key_exists($row[iPROTYPNAME], $typArr)) {
          $typArr[$row[iPROTYPNAME]] = $this->getTypId($row[iPROTYPNAME]);
        }
        $arr["protypid"] = $typArr[$row[iPROTYPNAME]];
        $arr["proname"] = $row[iPRONAME];
        $arr["pronames"] = $row[iPRONAMES];
        $arr["prokey"] = NStrings::webalize($row[iPROKEY]);
        $arr["protitle"] = $row[iPROTITLE];
        $arr["prodescs"] = $row[iPRODESCS];
        $arr["prodesc"] = $row[iPRODESC];
        $arr["prodescription"] = strip_tags($row[iPRODESCS]);
        $arr["prokeywords"] = strip_tags($row[iPROKEYWORDS]);
        $arr["proaccess"] = (int)$row[iPROACCESS];
        $arr["prowarranty"] = $row[iPROWARRANTY];
        $arr["propricecom"] = (double)$row[iPROPRICECOM];
        $arr["propricea"] = (double)$row[iPROPRICEA];
        $arr["propriceb"] = (double)$row[iPROPRICEB];
        $arr["propricec"] = (double)$row[iPROPRICEC];
        $arr["pronoteint"] = $row[iPRONOTEINT];
        //$arr["propriced"] = (double)$row[iPROPRICED];
        $arr["proweight"] = (double)$row[iPROWEIGHT];
        $arr["provatid"] = (int)$row[iPROVATID];
        $arr["propicname"] = trim($row[iPROPICNAME]);
        $arr["progoogleoff"] = (int)$row[iPROGOOGLEOFF];
        $arr["proorder"] = (int)$row[iPROORDER];
        $arr["prostatus"] = (int)$row[iPROSTATUS];
        if (isset($row[iPROBIGSIZE])) $arr["probigsize"] = (int)trim($row[iPROBIGSIZE]);
        if (isset($row[iPROOFFER])) $arr["prooffer"] = (int)trim($row[iPROOFFER]);
        
        //zjistim ID zbozi pokud existuje
        $proid = 0;
        if ((int)$row[iPROID] > 0) {
          $proid = (int)dibi::fetchSingle("SELECT proid from products WHERE proid=%i", (int)$row[iPROID]);
        }

        //kontrola duplicity
        $c = (int)dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM products WHERE procode='" . $arr["procode"] . "'" . ($proid > 0 ? " AND proid != " . $proid : ""));
        if ($c > 0) {
          $log[] = $arr["procode"]."|Duplicitní kód. Položka nebyla aktualizovaná.";
          $cntupdER ++;
          continue;
        }

        if ($proid > 0) {
          if ($products->update($proid, $arr)) {
            $cntupdOK ++;  
          } else {
            $cntupdER ++;
          }
        } else {
          $proid = $products->insert($arr);
          if ($proid >0) {
            $cntinsOK ++;                    
          } else {
            $cntinsER ++;
          }
        }
        //zarazeni do katalogu
        //nactu ID kategorie
        $row[iPROCATIDS] = trim($row[iPROCATIDS]);
        if (!empty($row[iPROCATIDS])) {
          //zarazeni do katalogu
          // Použij středník jako oddělovač pokud je přítomen, jinak čárku
          $delimiter = strpos($row[iPROCATIDS], ';') !== false ? ';' : ',';
          $catPaths = explode($delimiter, trim($row[iPROCATIDS], ",;"));
          $catIds = array();
          foreach ($catPaths as $catId) {
            //zjistim jestli kategorie existuje
            if (!empty($catId)) {
              $catid = dibi::fetchSingle("SELECT catid FROM catalogs WHERE catid=%i", $catId);
              if ($catid > 0) {
                $catIds[] = $catid;
              } else {
                $log[] = $arr["procode"]."|Nepodařilo se najít kategorii s ID  \"".$catId."\"";
              }
            }  
          }
          $products->updateCatPlace($proid, $catIds);  
        } else {
          if ($proid > 0) dibi::query("DELETE FROM catplaces WHERE capproid=%i", $proid);
        }
        //zkusim jestli nejsdou zadany nejake parametry
        $parRows = array();
        
        for($i=iPARAMS_START;true;$i=$i+2){
          if (!isset($row[$i]) || !isset($row[$i+1])) break;
          $parname=(string)$row[$i];
          $parvalue=(string)$row[$i+1];
          if (!empty($parname) && !empty($parvalue)) {
            $parRows[] = array('prpproid' => $proid, 'prpname' => $parname, 'prpvalue' => $parvalue);     
          } else {
            break;
          }
        }
        if (count($parRows) > 0) {
          //vamazu stavajici parametry a vlozim nove
          dibi::query("DELETE FROM proparams WHERE prpproid=%i", $proid);
          foreach ($parRows as $parRow) {
            $proparams->insert($parRow);  
          }
        }
        $products->genProKeyMaster($proid);  
      }
      
      $log[] = "";
      $log[] = "Nové záznamy: $cntinsOK bez chyb, $cntinsER chyb";
      $log[] = "Aktualizované záznamy: $cntupdOK bez chyb, $cntupdER chyb";
      $log[] = "Import dokončen.";
      //$catalogs->rebuildPaths();  
      $this->template->log = $log;
      //$this->flashMessage("Import dokončen.");
      //$this->redirect('this');
    }
    
  }
  
  public function exportSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $catName = "";
      if ($vals["catid"] > 0) {
        $catalog = dibi::fetch("SELECT * FROM catalogs WHERE catid=%i", $vals["catid"]); 
        $catName = $catalog->catname;
      } else {
        $catName = 'vse';
      }  
      /** PHPExcel_IOFactory */
      include LIBS_DIR.'/PHPExcel/PHPExcel.php';
      include LIBS_DIR.'/PHPExcel/PHPExcel/Writer/Excel5.php';
      
      $cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_to_phpTemp;
      $cacheSettings = array( 'memoryCacheSize' => '8MB');
      PHPExcel_Settings::setCacheStorageMethod($cacheMethod,$cacheSettings);
      
      $objPHPExcel = new PHPExcel();

      $objPHPExcel->setActiveSheetIndex(0);
      $cols = array(
        'id',
        'katalogové číslo',
        'objednávací číslo',
        'Název výrobce', 
        'Typ', 
        'Název', 
        'krátký popis', 
        'dlouhý popis', 
        'title', 
        'URL klíč', 
        'klíčové slova', 
        'Počet dní do dodání zboží, 0 - skladem', 
        'Záruka (volný text)', 
        'běžná cena(doporučená výrobcem)', 
        'Cena prodej(bez DPH)', 
        'Cena dealer(bez DPH)', 
        'Cena nákup(bez DPH)', 
        'Poznámka neviditelná', 
        'Sazba DPH (0-zakladní, 1-snížená)', 
        'Hmotnost (v Kg)', 
        'Název obrázku bez pripony (bez specialnich zaku jako jsou %, ?, /, ...)', 
        'Zařazení do katalogu - id kategorií oddělený čárkou',
        'Název pro vyhledávače (zbozi.cz, heureka.cz, google nákupy)',
        'Vyřadit z google nákupy (1-vyřadit, 0-nevyřadit)',
        'Nadrozměrné zboží (0-NE, 1-ANO)',
        'Poptávkové zboží (0-NE, 1-ANO)',
        'Status (0-aktivní, 1-neaktivni)',
        'Pořadí (jen číslo)',
        'URL zboží',
        'Parametry (nazev par. a hodnota par. do samostatnych sloupcu)'
      );
      $style_required = array(
        'fill' => array(
            'type' => PHPExcel_Style_Fill::FILL_SOLID,
            'color' => array('rgb'=>'00FF00'),
        ),
        'font' => array(
            'bold' => true,
        )
      );
      
      $style_head = array(
        'fill' => array(
            'type' => PHPExcel_Style_Fill::FILL_SOLID,
            'color' => array('rgb'=>'C0C0C0'),
        ),
        'font' => array(
            'bold' => true,
        )
      );

      $activeSheet = $objPHPExcel->getActiveSheet();                                                                                                                                                                                                                                                                            
      foreach ($cols as $index => $value) {
        $activeSheet->setCellValueByColumnAndRow($index,1, $value);
        switch ($index) {
           case 0:
           case 1:
           case 3:
           case 4:
           case 5:
           case 6:
           case 9:
           case 12:
           case iPROPRICEA:
             $activeSheet->getStyleByColumnAndRow($index,1)->applyFromArray( $style_required ); 
             break;
           default:
             $activeSheet->getStyleByColumnAndRow($index,1)->applyFromArray( $style_head ); 
        }
      }
      //nactu data
      $sql = "SELECT proid, prokey, prokeymaster, protitle, procode, procode2, manname, enuname AS typname, proname, pronames, prodesc, prodescs, 
prokeywords, proaccess, prowarranty, proweight, probigsize, prooffer,
propricecom, propricea, propriceb, propricec, propriced, pronoteint, propicname, provatid, progoogleoff, prostatus, proorder
FROM products 
INNER JOIN manufacturers ON (manid=promanid)
INNER JOIN enumcats ON (enuid=protypid AND enutypid=2)";
      if ($vals["catid"] > 0) {
        $sql .= "INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
WHERE catpathids LIKE '%|".$vals["catid"]."|%'";
      }
      $sql .= "
GROUP BY proid
ORDER BY proname";
      $rows = dibi::fetchAll($sql);
      $rowIndex = 1 ;
      
      foreach ($rows as $row) {
        $rowIndex++;
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROID, $rowIndex, $row->proid);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROCODE, $rowIndex, $row->procode);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROCODE2, $rowIndex, $row->procode2);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROMANNAME, $rowIndex, $row->manname);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROTYPNAME, $rowIndex, $row->typname);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPRONAME, $rowIndex, $row->proname);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPRONAMES, $rowIndex, $row->pronames);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPRODESCS, $rowIndex, $row->prodescs);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPRODESC, $rowIndex, $row->prodesc);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROTITLE, $rowIndex, $row->protitle);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROKEY, $rowIndex, $row->prokey);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROKEYWORDS, $rowIndex, $row->prokeywords);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROACCESS, $rowIndex, $row->proaccess);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROWARRANTY, $rowIndex, $row->prowarranty);
        $activeSheet->setCellValueByColumnAndRow(iPROPRICECOM, $rowIndex, $row->propricecom);
        $activeSheet->setCellValueByColumnAndRow(iPROPRICEA, $rowIndex, $row->propricea);
        $activeSheet->setCellValueByColumnAndRow(iPROPRICEB, $rowIndex, $row->propriceb);
        $activeSheet->setCellValueByColumnAndRow(iPROPRICEC, $rowIndex, $row->propricec);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPRONOTEINT, $rowIndex, $row->pronoteint);
        $activeSheet->setCellValueByColumnAndRow(iPROVATID, $rowIndex, $row->provatid);
        $activeSheet->setCellValueByColumnAndRow(iPROWEIGHT, $rowIndex, $row->proweight);
        $activeSheet->setCellValueExplicitByColumnAndRow(iPROPICNAME, $rowIndex, trim($row->propicname));
        $activeSheet->setCellValueByColumnAndRow(iPROBIGSIZE, $rowIndex, trim($row->probigsize));
        $activeSheet->setCellValueByColumnAndRow(iPROOFFER, $rowIndex, trim($row->prooffer));
        $activeSheet->setCellValueByColumnAndRow(iPROGOOGLEOFF, $rowIndex, trim($row->progoogleoff));
        $activeSheet->setCellValueByColumnAndRow(iPROSTATUS, $rowIndex, trim($row->prostatus));
        $activeSheet->setCellValueByColumnAndRow(iPROORDER, $rowIndex, trim($row->proorder));
        $prokey = (!empty($row->prokey) ? $row->prokey : NStrings::webalize($row->proname));
        $prokeymaster = "";
        if (!empty($row->prokeymaster)) {
          $prokeymaster = $row->prokeymaster."/";  
        }
        $url = 'https://www.dvaptaci.cz/'.$prokeymaster.$prokey.'/p'.$row->proid;
        $activeSheet->setCellValueByColumnAndRow(iPROURL, $rowIndex, $url);
        
        //sestavim ID kategorii ve kterych je zbozi zarazeno
        $crows = dibi::fetchAll("
          SELECT catid, catname
          FROM catalogs
          INNER JOIN catplaces ON (capcatid=catid)
          WHERE capproid=%i
          GROUP BY catid
          ORDER BY catorder", $row->proid);
        $catids = "";
        foreach ($crows as $catrow) {
          $catids .= $catrow->catid.';';
        } 
        $catids = trim($catids, ';');
        $activeSheet->setCellValueByColumnAndRow(iPROCATIDS, $rowIndex, $catids);
        
        //naplnim parametry zbozi
        $proParams = dibi::fetchAll("SELECT * FROM proparams WHERE prpproid=%i", $row->proid, " ORDER BY prpid");
        $parCnt = iPARAMS_START; 
        foreach ($proParams as $parRow) {
          $activeSheet->setCellValueByColumnAndRow($parCnt, $rowIndex, $parRow->prpname);
          $parCnt++;
          $activeSheet->setCellValueByColumnAndRow($parCnt, $rowIndex, $parRow->prpvalue);
          $parCnt++;
        }
      }
      
      $activeSheet->setTitle('Export');
      $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
      $objWriter->setTempDir(TEMP_DIR);
      $fName = TEMP_DIR."/".NStrings::webalize($catName).".xls";
      $objWriter->save($fName);
      if (file_exists($fName)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename='.NStrings::webalize($catName).".xls");
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($fName));
        ob_clean();
        flush();
        readfile($fName);
      }
    }  
  }
  
  public function renderDefault() {
      
  }
  
  /********************* facilities *********************/

  protected function createComponentImportForm() {
  
    $form = new NAppForm();
    
    $form->addUpload('imp_file', "Importní soubor", 100)
      ->addRule(NForm::FILLED, "Vyberte importní soubor");
    
    $arr = array(
      'XLS'=>'Excel 5 [*.xls]',
    );
    $form->addSelect('format', 'Formát importního souboru', $arr)
      ->setPrompt('...')
      ->setDefaultValue('XLS')
      ->addRule(NForm::FILLED, "Vyberte formát importního souboru"); 
    
    $form->addSubmit('save', 'Importovat');    
    $form->onSuccess[] = array($this, 'importSubmitted');
    return $form;  
  }
  
  protected function createComponentExportForm() {
  
    $form = new NAppForm();
    $catalogs = new CatalogsModel();
    $arr = $catalogs->getEnumCatalogCombo();
    $arr[0] = "Vše";
    $form->addSelect('catid', 'Katalog:', $arr)
      ->addRule(NForm::FILLED, 'Prosím vyplňte katalog.');
    
    $form->addSubmit('save', 'Exportovat');    
    $form->onSuccess[] = array($this, 'exportSubmitted');
    return $form;  
  }
  
  private function getManId($name) {
    $manid = dibi::fetchSingle("SELECT manid FROM manufacturers WHERE manname=%s", $name);
    if ($manid > 0) {
    } else {
      $manufacturers = new ManufacturersModel();
      $manid = $manufacturers->insert(array('manname'=>$name));
    }
    return($manid);
    
  }
  
  private function getTypId($name) {
    $enuid = dibi::fetchSingle("SELECT enuid FROM enumcats WHERE enutypid=2 AND enuname=%s", $name);
    if ($enuid > 0) {
    } else {
      $enumcats = new EnumcatsModel();
      $enuid = $enumcats->insert(array('enutypid'=>2, 'enuname'=>$name));
    }
    return($enuid);
    
  }
  
  private function prepStr($str) {
    $str = trim($str, '"');
    $str = ereg_replace('""', '"', $str);
    return $str;
  }
  
  public function actionGenProKeyMaster() {
    $pros = new ProductsModel();
    $pros->genProKeyMaster();
    $this->terminate();
  }
}